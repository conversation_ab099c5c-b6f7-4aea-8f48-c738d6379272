package main

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"time"
	"wechatdll/Cilent/wechat"
	"wechatdll/models/Friend"
)

// 测试配置
type TestConfig struct {
	Wxid     string `json:"wxid"`     // 测试用的微信ID
	TestUsers []TestUser `json:"test_users"` // 测试用户列表
}

type TestUser struct {
	UserName    string `json:"username"`    // 用户名
	ExpectedStatus string `json:"expected"`  // 期望的状态
	Description string `json:"description"` // 描述
}

// 默认测试配置
func getDefaultTestConfig() TestConfig {
	return TestConfig{
		Wxid: "请替换为实际的测试微信ID",
		TestUsers: []TestUser{
			{
				UserName:    "filehelper",
				ExpectedStatus: "normal",
				Description: "文件传输助手",
			},
			{
				UserName:    "weixin",
				ExpectedStatus: "normal", 
				Description: "微信团队",
			},
			{
				UserName:    "nonexistent_user_12345",
				ExpectedStatus: "unknown",
				Description: "不存在的用户",
			},
			// 添加更多测试用例...
		},
	}
}

func main() {
	fmt.Println("=== 微信好友关系API测试工具 ===")
	fmt.Println("用于验证好友关系检测接口是否正常工作")
	fmt.Println()

	// 读取或创建测试配置
	config := loadOrCreateConfig()
	
	if config.Wxid == "请替换为实际的测试微信ID" {
		fmt.Println("❌ 请先在 test_config.json 中配置正确的测试微信ID")
		fmt.Println("💡 提示：修改 test_config.json 文件中的 wxid 字段")
		return
	}

	fmt.Printf("测试微信ID: %s\n", config.Wxid)
	fmt.Printf("测试用户数量: %d\n", len(config.TestUsers))
	fmt.Println()

	// 执行测试
	runTests(config)
}

func loadOrCreateConfig() TestConfig {
	configFile := "test_config.json"
	
	// 尝试读取配置文件
	if data, err := os.ReadFile(configFile); err == nil {
		var config TestConfig
		if json.Unmarshal(data, &config) == nil {
			fmt.Printf("✅ 已加载配置文件: %s\n", configFile)
			return config
		}
	}
	
	// 创建默认配置文件
	config := getDefaultTestConfig()
	if data, err := json.MarshalIndent(config, "", "  "); err == nil {
		os.WriteFile(configFile, data, 0644)
		fmt.Printf("📝 已创建默认配置文件: %s\n", configFile)
		fmt.Printf("💡 请修改配置文件中的测试参数\n")
	}
	
	return config
}

func runTests(config TestConfig) {
	fmt.Println("开始执行测试...")
	fmt.Println("=" + string(make([]byte, 60)) + "=")
	
	var results []TestResult
	
	for i, testUser := range config.TestUsers {
		fmt.Printf("\n🧪 测试 %d/%d: %s (%s)\n", 
			i+1, len(config.TestUsers), testUser.Description, testUser.UserName)
		
		result := testSingleUser(config.Wxid, testUser)
		results = append(results, result)
		
		// 添加延迟避免请求过快
		time.Sleep(1 * time.Second)
	}
	
	// 输出测试总结
	printTestSummary(results)
}

type TestResult struct {
	UserName       string
	Description    string
	Success        bool
	FriendRelation uint32
	BaseResponseRet int32
	ErrorMessage   string
	ResponseData   interface{}
}

func testSingleUser(wxid string, testUser TestUser) TestResult {
	result := TestResult{
		UserName:    testUser.UserName,
		Description: testUser.Description,
	}
	
	// 调用好友关系检测API
	apiResult := Friend.FriendRelation(Friend.FriendRelationParam{
		Wxid:     wxid,
		UserName: testUser.UserName,
	})
	
	result.Success = apiResult.Success
	
	if !apiResult.Success {
		result.ErrorMessage = apiResult.Message
		fmt.Printf("❌ 请求失败: %s\n", apiResult.Message)
		return result
	}
	
	// 解析响应数据
	if apiResult.Data != nil {
		response, ok := apiResult.Data.(wechat.MMBizJsApiGetUserOpenIdResponse)
		if ok {
			result.FriendRelation = response.GetFriendRelation()
			result.BaseResponseRet = response.GetBaseResponse().GetRet()
			result.ResponseData = response
			
			// 输出详细信息
			fmt.Printf("📊 结果分析:\n")
			fmt.Printf("   FriendRelation: %d (%s)\n", 
				result.FriendRelation, interpretFriendRelation(result.FriendRelation))
			fmt.Printf("   BaseResponse.Ret: %d\n", result.BaseResponseRet)
			fmt.Printf("   Openid: %s\n", response.GetOpenid())
			fmt.Printf("   NickName: %s\n", response.GetNickName())
			
			// 分析结果
			analyzeResult(result)
			
		} else {
			result.ErrorMessage = "响应数据类型转换失败"
			fmt.Printf("❌ %s\n", result.ErrorMessage)
		}
	} else {
		result.ErrorMessage = "响应数据为空"
		fmt.Printf("❌ %s\n", result.ErrorMessage)
	}
	
	return result
}

func interpretFriendRelation(code uint32) string {
	switch code {
	case 0:
		return "正常好友关系 或 默认值"
	case 1:
		return "已删除好友"
	case 4:
		return "自己拉黑对方"
	case 5:
		return "被对方拉黑"
	case 1450:
		return "非好友关系"
	default:
		return fmt.Sprintf("未知状态码: %d", code)
	}
}

func analyzeResult(result TestResult) {
	if result.BaseResponseRet != 0 {
		fmt.Printf("⚠️  微信服务器返回错误码: %d\n", result.BaseResponseRet)
		fmt.Printf("   可能表示API已被官方限制或禁用\n")
		return
	}
	
	if result.FriendRelation == 0 {
		fmt.Printf("🤔 FriendRelation为0，可能原因:\n")
		fmt.Printf("   1. 确实是正常好友关系\n")
		fmt.Printf("   2. 微信官方更改了返回逻辑\n")
		fmt.Printf("   3. API功能被限制\n")
		fmt.Printf("   4. 需要特殊权限或参数\n")
	} else {
		fmt.Printf("✅ 返回了有效的FriendRelation值: %d\n", result.FriendRelation)
	}
}

func printTestSummary(results []TestResult) {
	fmt.Println("\n" + "=" + string(make([]byte, 60)) + "=")
	fmt.Println("📋 测试总结报告")
	fmt.Println("=" + string(make([]byte, 60)) + "=")
	
	successCount := 0
	zeroRelationCount := 0
	nonZeroRelationCount := 0
	errorCount := 0
	
	for _, result := range results {
		if !result.Success {
			errorCount++
		} else {
			successCount++
			if result.FriendRelation == 0 {
				zeroRelationCount++
			} else {
				nonZeroRelationCount++
			}
		}
	}
	
	fmt.Printf("总测试数: %d\n", len(results))
	fmt.Printf("成功请求: %d\n", successCount)
	fmt.Printf("失败请求: %d\n", errorCount)
	fmt.Printf("FriendRelation=0: %d\n", zeroRelationCount)
	fmt.Printf("FriendRelation≠0: %d\n", nonZeroRelationCount)
	
	fmt.Println("\n🔍 详细结果:")
	for _, result := range results {
		status := "❌"
		if result.Success {
			status = "✅"
		}
		fmt.Printf("%s %s (%s): FriendRelation=%d\n", 
			status, result.Description, result.UserName, result.FriendRelation)
	}
	
	// 给出结论
	fmt.Println("\n🎯 结论分析:")
	if errorCount == len(results) {
		fmt.Println("❌ 所有请求都失败，可能是:")
		fmt.Println("   1. 登录状态失效")
		fmt.Println("   2. 网络连接问题") 
		fmt.Println("   3. API接口已被微信官方完全禁用")
	} else if zeroRelationCount == successCount {
		fmt.Println("⚠️  所有成功请求的FriendRelation都为0，可能是:")
		fmt.Println("   1. 微信官方更改了API返回逻辑")
		fmt.Println("   2. 该功能已被限制或需要特殊权限")
		fmt.Println("   3. 需要调整请求参数")
	} else if nonZeroRelationCount > 0 {
		fmt.Println("✅ API仍然有效，能够返回不同的好友关系状态")
		fmt.Println("   建议检查返回0的具体原因")
	}
	
	fmt.Println("\n💡 建议:")
	fmt.Println("1. 使用真实的好友/非好友账号进行测试")
	fmt.Println("2. 检查登录状态是否有效")
	fmt.Println("3. 尝试不同的请求参数组合")
	fmt.Println("4. 监控微信官方的API变更公告")
}
